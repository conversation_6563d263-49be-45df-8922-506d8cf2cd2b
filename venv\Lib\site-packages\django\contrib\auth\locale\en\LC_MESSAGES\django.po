# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: contrib/auth/admin.py:46
msgid "Personal info"
msgstr ""

#: contrib/auth/admin.py:47
msgid "Permissions"
msgstr ""

#: contrib/auth/admin.py:50
msgid "Important dates"
msgstr ""

#: contrib/auth/admin.py:134
#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr ""

#: contrib/auth/admin.py:144
msgid "Password changed successfully."
msgstr ""

#: contrib/auth/admin.py:164
#, python-format
msgid "Change password: %s"
msgstr ""

#: contrib/auth/apps.py:15
msgid "Authentication and Authorization"
msgstr ""

#: contrib/auth/base_user.py:48
msgid "password"
msgstr ""

#: contrib/auth/base_user.py:49
msgid "last login"
msgstr ""

#: contrib/auth/forms.py:31
msgid "No password set."
msgstr ""

#: contrib/auth/forms.py:36
msgid "Invalid password format or unknown hashing algorithm."
msgstr ""

#: contrib/auth/forms.py:78 contrib/auth/forms.py:316 contrib/auth/forms.py:389
msgid "The two password fields didn’t match."
msgstr ""

#: contrib/auth/forms.py:81 contrib/auth/forms.py:134 contrib/auth/forms.py:170
#: contrib/auth/forms.py:393
msgid "Password"
msgstr ""

#: contrib/auth/forms.py:87
msgid "Password confirmation"
msgstr ""

#: contrib/auth/forms.py:90 contrib/auth/forms.py:402
msgid "Enter the same password as before, for verification."
msgstr ""

#: contrib/auth/forms.py:136
msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\">this form</a>."
msgstr ""

#: contrib/auth/forms.py:177
#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""

#: contrib/auth/forms.py:180
msgid "This account is inactive."
msgstr ""

#: contrib/auth/forms.py:241
msgid "Email"
msgstr ""

#: contrib/auth/forms.py:319
msgid "New password"
msgstr ""

#: contrib/auth/forms.py:325
msgid "New password confirmation"
msgstr ""

#: contrib/auth/forms.py:361
msgid "Your old password was entered incorrectly. Please enter it again."
msgstr ""

#: contrib/auth/forms.py:364
msgid "Old password"
msgstr ""

#: contrib/auth/forms.py:399
msgid "Password (again)"
msgstr ""

#: contrib/auth/hashers.py:259 contrib/auth/hashers.py:333
#: contrib/auth/hashers.py:429 contrib/auth/hashers.py:489
#: contrib/auth/hashers.py:520 contrib/auth/hashers.py:556
#: contrib/auth/hashers.py:592 contrib/auth/hashers.py:630
msgid "algorithm"
msgstr ""

#: contrib/auth/hashers.py:260
msgid "iterations"
msgstr ""

#: contrib/auth/hashers.py:261 contrib/auth/hashers.py:339
#: contrib/auth/hashers.py:431 contrib/auth/hashers.py:490
#: contrib/auth/hashers.py:521 contrib/auth/hashers.py:631
msgid "salt"
msgstr ""

#: contrib/auth/hashers.py:262 contrib/auth/hashers.py:340
#: contrib/auth/hashers.py:491 contrib/auth/hashers.py:522
#: contrib/auth/hashers.py:557 contrib/auth/hashers.py:593
#: contrib/auth/hashers.py:632
msgid "hash"
msgstr ""

#: contrib/auth/hashers.py:334
msgid "variety"
msgstr ""

#: contrib/auth/hashers.py:335
msgid "version"
msgstr ""

#: contrib/auth/hashers.py:336
msgid "memory cost"
msgstr ""

#: contrib/auth/hashers.py:337
msgid "time cost"
msgstr ""

#: contrib/auth/hashers.py:338
msgid "parallelism"
msgstr ""

#: contrib/auth/hashers.py:430
msgid "work factor"
msgstr ""

#: contrib/auth/hashers.py:432
msgid "checksum"
msgstr ""

#: contrib/auth/models.py:56 contrib/auth/models.py:108
msgid "name"
msgstr ""

#: contrib/auth/models.py:60
msgid "content type"
msgstr ""

#: contrib/auth/models.py:62
msgid "codename"
msgstr ""

#: contrib/auth/models.py:67
msgid "permission"
msgstr ""

#: contrib/auth/models.py:68 contrib/auth/models.py:111
msgid "permissions"
msgstr ""

#: contrib/auth/models.py:118
msgid "group"
msgstr ""

#: contrib/auth/models.py:119 contrib/auth/models.py:242
msgid "groups"
msgstr ""

#: contrib/auth/models.py:233
msgid "superuser status"
msgstr ""

#: contrib/auth/models.py:236
msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr ""

#: contrib/auth/models.py:245
msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""

#: contrib/auth/models.py:253
msgid "user permissions"
msgstr ""

#: contrib/auth/models.py:255
msgid "Specific permissions for this user."
msgstr ""

#: contrib/auth/models.py:326
msgid "username"
msgstr ""

#: contrib/auth/models.py:329
msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""

#: contrib/auth/models.py:332
msgid "A user with that username already exists."
msgstr ""

#: contrib/auth/models.py:335
msgid "first name"
msgstr ""

#: contrib/auth/models.py:336
msgid "last name"
msgstr ""

#: contrib/auth/models.py:337
msgid "email address"
msgstr ""

#: contrib/auth/models.py:339
msgid "staff status"
msgstr ""

#: contrib/auth/models.py:341
msgid "Designates whether the user can log into this admin site."
msgstr ""

#: contrib/auth/models.py:344
msgid "active"
msgstr ""

#: contrib/auth/models.py:347
msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""

#: contrib/auth/models.py:351
msgid "date joined"
msgstr ""

#: contrib/auth/models.py:360
msgid "user"
msgstr ""

#: contrib/auth/models.py:361
msgid "users"
msgstr ""

#: contrib/auth/password_validation.py:102
#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
msgstr[1] ""

#: contrib/auth/password_validation.py:112
#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] ""
msgstr[1] ""

#: contrib/auth/password_validation.py:151
#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr ""

#: contrib/auth/password_validation.py:157
msgid "Your password can’t be too similar to your other personal information."
msgstr ""

#: contrib/auth/password_validation.py:183
msgid "This password is too common."
msgstr ""

#: contrib/auth/password_validation.py:188
msgid "Your password can’t be a commonly used password."
msgstr ""

#: contrib/auth/password_validation.py:198
msgid "This password is entirely numeric."
msgstr ""

#: contrib/auth/password_validation.py:203
msgid "Your password can’t be entirely numeric."
msgstr ""

#: contrib/auth/templates/registration/password_reset_subject.txt:2
#, python-format
msgid "Password reset on %(site_name)s"
msgstr ""

#: contrib/auth/validators.py:12
msgid ""
"Enter a valid username. This value may contain only English letters, "
"numbers, and @/./+/-/_ characters."
msgstr ""

#: contrib/auth/validators.py:22
msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""

#: contrib/auth/views.py:160
msgid "Logged out"
msgstr ""

#: contrib/auth/views.py:217
msgid "Password reset"
msgstr ""

#: contrib/auth/views.py:244
msgid "Password reset sent"
msgstr ""

#: contrib/auth/views.py:254
msgid "Enter new password"
msgstr ""

#: contrib/auth/views.py:314
msgid "Password reset unsuccessful"
msgstr ""

#: contrib/auth/views.py:322
msgid "Password reset complete"
msgstr ""

#: contrib/auth/views.py:334
msgid "Password change"
msgstr ""

#: contrib/auth/views.py:357
msgid "Password change successful"
msgstr ""
