{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app_name }} Payment - Dry Fish Shop</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .payment-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 3rem;
            max-width: 500px;
            width: 100%;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .app-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 20px 40px rgba(76, 175, 80, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 20px 40px rgba(76, 175, 80, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 25px 50px rgba(76, 175, 80, 0.4);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 20px 40px rgba(76, 175, 80, 0.3);
            }
        }
        
        .app-icon i {
            font-size: 3rem;
            color: white;
        }
        
        .payment-title {
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50 0%, #4CAF50 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .payment-subtitle {
            color: #64748b;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }
        
        .order-summary {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .order-summary h3 {
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .order-summary p {
            color: #64748b;
            margin-bottom: 0.8rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .order-summary .total {
            border-top: 2px solid #e2e8f0;
            padding-top: 1rem;
            margin-top: 1rem;
            font-weight: 600;
            font-size: 1.2rem;
            color: #2c3e50;
        }
        
        .app-redirect-section {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border: 2px solid #4CAF50;
            border-radius: 16px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .app-redirect-section h3 {
            color: #2e7d32;
            margin-bottom: 1rem;
            font-size: 1.4rem;
        }
        
        .app-redirect-section p {
            color: #388e3c;
            margin-bottom: 1.5rem;
            font-size: 1rem;
        }
        
        .open-app-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 1.5rem 3rem;
            border: none;
            border-radius: 16px;
            font-size: 1.3rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 1rem 0;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-block;
        }
        
        .open-app-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .open-app-button:hover::before {
            left: 100%;
        }
        
        .open-app-button:hover {
            background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(76, 175, 80, 0.4);
        }
        
        .fallback-section {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .fallback-section h4 {
            color: #856404;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .fallback-section p {
            color: #856404;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .fallback-button {
            background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
            color: #856404;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-decoration: none;
            display: inline-block;
        }
        
        .fallback-button:hover {
            background: linear-gradient(135deg, #ffb300 0%, #ffc107 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
        }
        
        .instructions {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196F3;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .instructions h4 {
            color: #1976D2;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .instructions ol {
            color: #424242;
            margin-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-top: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: #5a6fd8;
            transform: translateX(-5px);
        }
        
        .countdown {
            font-size: 1.1rem;
            color: #4CAF50;
            font-weight: 600;
            margin: 1rem 0;
        }
        
        @media (max-width: 768px) {
            .payment-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .payment-title {
                font-size: 1.8rem;
            }
            
            .order-summary {
                padding: 1.5rem;
            }
            
            .app-icon {
                width: 80px;
                height: 80px;
            }
            
            .app-icon i {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="app-icon">
            <i class="{{ app_icon }}"></i>
        </div>
        
        <h1 class="payment-title">{{ app_name }} Payment</h1>
        <p class="payment-subtitle">Complete your payment securely with {{ app_name }}</p>
        
        <div class="order-summary">
            <h3><i class="fas fa-receipt"></i> Order Summary</h3>
            <p><span>Product:</span> <span>{{ order.get_product_display }}</span></p>
            <p><span>Quantity:</span> <span>{{ order.quantity }} kg</span></p>
            <p><span>Customer:</span> <span>{{ order.name }}</span></p>
            <p><span>Mobile:</span> <span>{{ order.mobile }}</span></p>
            <p class="total"><span>Total Amount:</span> <span>₹{{ order.total_amount }}</span></p>
        </div>
        
        <div class="app-redirect-section">
            <h3><i class="{{ app_icon }}"></i> Open {{ app_name }}</h3>
            <p>Click the button below to open {{ app_name }} and complete your payment</p>
            <div class="countdown" id="countdown">Redirecting in <span id="timer">5</span> seconds...</div>
            <a href="{{ app_url }}" class="open-app-button" id="open-app">
                <i class="{{ app_icon }}"></i> Open {{ app_name }} Now
            </a>
        </div>
        
        <div class="instructions">
            <h4><i class="fas fa-info-circle"></i> Payment Instructions</h4>
            <ol>
                <li>Click "Open {{ app_name }} Now" button above</li>
                <li>{{ app_name }} app will open automatically</li>
                <li>Verify payment details (₹{{ order.total_amount }} to jawaharprasath@paytm)</li>
                <li>Complete the payment using your UPI PIN</li>
                <li>Return to this page after payment completion</li>
            </ol>
        </div>
        
        <div class="fallback-section">
            <h4><i class="fas fa-exclamation-triangle"></i> App Not Opening?</h4>
            <p>If {{ app_name }} doesn't open automatically, you can use any UPI app to pay:</p>
            <a href="{{ fallback_url }}" class="fallback-button">
                <i class="fas fa-qr-code"></i> Use Any UPI App
            </a>
        </div>
        
        <a href="{% url 'payment' order.id %}" class="back-link">
            <i class="fas fa-arrow-left"></i> Choose Different Payment Method
        </a>
    </div>

    <script>
        // Auto-redirect countdown
        let timeLeft = 5;
        const timerElement = document.getElementById('timer');
        const countdownElement = document.getElementById('countdown');
        const openAppButton = document.getElementById('open-app');
        
        const countdown = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(countdown);
                countdownElement.style.display = 'none';
                // Auto-click the app button
                window.location.href = "{{ app_url }}";
            }
        }, 1000);
        
        // Manual click tracking
        openAppButton.addEventListener('click', function() {
            clearInterval(countdown);
            countdownElement.innerHTML = '<i class="fas fa-check-circle"></i> Opening {{ app_name }}...';
            countdownElement.style.color = '#4CAF50';
        });
        
        // Check if user returned from app
        window.addEventListener('focus', function() {
            setTimeout(function() {
                // Show return message
                const returnMessage = document.createElement('div');
                returnMessage.innerHTML = `
                    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 1rem; margin: 1rem 0; color: #155724;">
                        <i class="fas fa-info-circle"></i> Payment completed? 
                        <a href="{% url 'view_orders' %}" style="color: #155724; font-weight: bold; text-decoration: underline;">
                            Check your orders here
                        </a>
                    </div>
                `;
                document.querySelector('.payment-container').appendChild(returnMessage);
            }, 2000);
        });
    </script>
</body>
</html>
