{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - JP Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .payment-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 3rem;
            max-width: 500px;
            width: 100%;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .payment-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 15px 40px rgba(76, 175, 80, 0.3);
        }
        
        .payment-icon i {
            font-size: 2.5rem;
            color: white;
        }
        
        .payment-title {
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50 0%, #4CAF50 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .order-summary {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .order-summary h3 {
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .order-summary p {
            color: #64748b;
            margin-bottom: 0.8rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .order-summary .total {
            border-top: 2px solid #e2e8f0;
            padding-top: 1rem;
            margin-top: 1rem;
            font-weight: 600;
            font-size: 1.2rem;
            color: #2c3e50;
        }
        
        .payment-method-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196F3;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .payment-method-info i {
            font-size: 2rem;
            color: #2196F3;
        }
        
        .payment-method-info div h4 {
            color: #1976D2;
            margin-bottom: 0.5rem;
        }
        
        .payment-method-info div p {
            color: #424242;
            font-size: 0.9rem;
        }
        
        .pay-button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 1.5rem 3rem;
            border: none;
            border-radius: 16px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .pay-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .pay-button:hover::before {
            left: 100%;
        }
        
        .pay-button:hover {
            background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(76, 175, 80, 0.4);
        }
        
        .pay-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            margin: 2rem 0;
        }
        
        .loading i {
            animation: spin 1s linear infinite;
            color: #4CAF50;
            font-size: 2rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .security-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 12px;
            padding: 1rem;
            margin-top: 2rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-size: 0.9rem;
            color: #856404;
        }
        
        .security-info i {
            color: #ffc107;
            font-size: 1.1rem;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            margin-top: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: #5a6fd8;
            transform: translateX(-5px);
        }
        
        @media (max-width: 768px) {
            .payment-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .payment-title {
                font-size: 1.8rem;
            }
            
            .order-summary {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-icon">
            <i class="fas fa-credit-card"></i>
        </div>
        
        <h1 class="payment-title">Complete Payment</h1>
        
        <div class="order-summary">
            <h3><i class="fas fa-receipt"></i> Order Summary</h3>
            <p><span>Product:</span> <span>{{ order.get_product_display }}</span></p>
            <p><span>Quantity:</span> <span>{{ order.quantity }} kg</span></p>
            <p><span>Customer:</span> <span>{{ order.name }}</span></p>
            <p><span>Mobile:</span> <span>{{ order.mobile }}</span></p>
            <p class="total"><span>Total Amount:</span> <span>₹{{ order.total_amount }}</span></p>
        </div>
        
        <div class="payment-method-info">
            {% if payment_method == 'google_pay' %}
                <i class="fab fa-google-pay"></i>
                <div>
                    <h4>Google Pay</h4>
                    <p>Secure payment via Google Pay</p>
                </div>
            {% elif payment_method == 'phonepe' %}
                <i class="fas fa-mobile-alt"></i>
                <div>
                    <h4>PhonePe</h4>
                    <p>Pay using PhonePe UPI</p>
                </div>
            {% elif payment_method == 'paytm' %}
                <i class="fas fa-wallet"></i>
                <div>
                    <h4>Paytm</h4>
                    <p>Pay using Paytm wallet or UPI</p>
                </div>
            {% endif %}
        </div>
        
        <button id="pay-button" class="pay-button">
            <i class="fas fa-lock"></i> Pay ₹{{ order.total_amount }} Securely
        </button>
        
        <div id="loading" class="loading">
            <i class="fas fa-spinner"></i>
            <p>Processing payment...</p>
        </div>
        
        <div class="security-info">
            <i class="fas fa-shield-alt"></i>
            {% if razorpay_order_id|slice:":10" == "order_demo" %}
                <span>🧪 DEMO MODE: This is a test payment - no real money will be charged</span>
            {% else %}
                <span>Your payment is secured with 256-bit SSL encryption</span>
            {% endif %}
        </div>
        
        <a href="{% url 'payment' order.id %}" class="back-link">
            <i class="fas fa-arrow-left"></i> Choose Different Payment Method
        </a>
    </div>

    <script>
        document.getElementById('pay-button').onclick = function(e) {
            e.preventDefault();

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('pay-button').disabled = true;

            // Check if we're in demo mode
            var orderId = "{{ razorpay_order_id }}";
            if (orderId.startsWith('order_demo_')) {
                // Demo mode - simulate payment success
                setTimeout(function() {
                    fetch('{% url "payment_verify" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: 'razorpay_payment_id=demo_payment_' + Date.now() +
                              '&razorpay_order_id=' + orderId +
                              '&razorpay_signature=demo_signature'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            window.location.href = data.redirect_url;
                        } else {
                            alert('Payment verification failed: ' + data.message);
                            hideLoading();
                        }
                    })
                    .catch(error => {
                        alert('Payment verification error: ' + error);
                        hideLoading();
                    });
                }, 2000); // Simulate 2 second processing time
                return;
            }

            var options = {
                "key": "{{ razorpay_key_id }}",
                "amount": "{{ amount }}",
                "currency": "{{ currency }}",
                "name": "{{ customer_name }}",
                "description": "{{ order.get_product_display }} - {{ order.quantity }}kg",
                "order_id": "{{ razorpay_order_id }}",
                "handler": function (response) {
                    // Payment successful
                    fetch('{% url "payment_verify" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: 'razorpay_payment_id=' + response.razorpay_payment_id + 
                              '&razorpay_order_id=' + response.razorpay_order_id + 
                              '&razorpay_signature=' + response.razorpay_signature
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            window.location.href = data.redirect_url;
                        } else {
                            alert('Payment verification failed: ' + data.message);
                            hideLoading();
                        }
                    })
                    .catch(error => {
                        alert('Payment verification error: ' + error);
                        hideLoading();
                    });
                },
                "prefill": {
                    "name": "{{ customer_name }}",
                    "contact": "{{ customer_mobile }}",
                    "email": "{{ customer_email }}"
                },
                "theme": {
                    "color": "#4CAF50"
                },
                "modal": {
                    "ondismiss": function() {
                        hideLoading();
                    }
                }
            };
            
            var rzp1 = new Razorpay(options);
            rzp1.open();
        };
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('pay-button').disabled = false;
        }
    </script>
</body>
</html>
