{% load socialaccount %}
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Dry Fish Shop</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> Dry Fish Shop
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/#products">Products</a></li>
                <li><a href="/order/">Order</a></li>
                <li><a href="/accounts/login/" class="active">Login</a></li>
            </ul>
        </div>
    </nav>

    <div class="auth-container">
        <div class="auth-card fade-in-up">
            <div class="auth-header">
                <i class="fas fa-fish" style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;"></i>
                <h2>Welcome Back!</h2>
                <p style="color: #6b7280;">Sign in to your account to place orders</p>
            </div>

            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="message error" style="margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-circle"></i> {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Google OAuth Login -->
            <a href="{% provider_login_url 'google' %}" class="google-btn">
                <i class="fab fa-google"></i>
                Continue with Google
            </a>

            <!-- Divider -->
            <div class="divider">
                <span>or</span>
            </div>

            <!-- Email/Password Login Form -->
            <form method="post">
                {% csrf_token %}

                {% if form.errors %}
                    <div class="message error" style="margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-circle"></i>
                        Please correct the errors below:
                        {{ form.errors }}
                    </div>
                {% endif %}

                <div class="form-group">
                    <label for="{{ form.login.id_for_label }}">
                        <i class="fas fa-envelope"></i> Email
                    </label>
                    {{ form.login }}
                </div>

                <div class="form-group">
                    <label for="{{ form.password.id_for_label }}">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    {{ form.password }}
                </div>

                <div class="form-group">
                    <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                        <input type="checkbox" name="remember" style="margin: 0;">
                        Remember me
                    </label>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    <i class="fas fa-sign-in-alt"></i> Login with Email
                </button>

                <input type="hidden" name="next" value="{{ next }}" />
            </form>

            <!-- Links -->
            <div style="text-align: center; margin-top: 1.5rem;">
                <p style="color: #6b7280; margin-bottom: 1rem;">
                    Don't have an account?
                    <a href="/accounts/signup/" style="color: #667eea; text-decoration: none;">Sign up</a>
                </p>
                <a href="/" style="color: #667eea; text-decoration: none;">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </a>
            </div>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/91XXXXXXXXXX" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Add some interactive effects
        document.querySelectorAll('.form-control, input[type="email"], input[type="password"]').forEach(input => {
            input.classList.add('form-control');
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
