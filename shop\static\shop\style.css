body {
    font-family: <PERSON><PERSON>;
    padding: 20px;
    background: #f4f4f4;
}
h1 {
    color: #333;
}
.product-grid {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}
.product-card {
    background: white;
    padding: 15px;
    border: 1px solid #ccc;
    width: 280px;
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}
.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.product-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}
.product-card h2 {
    color: #2c3e50;
    margin: 10px 0;
    font-size: 1.2em;
}
.product-description {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}
.product-description p {
    color: #555;
    font-style: italic;
    font-size: 0.9em;
    line-height: 1.4;
    margin: 0;
    text-align: left;
}
.order-btn {
    display: inline-block;
    margin-top: 20px;
    background: green;
    color: white;
    padding: 10px;
    text-decoration: none;
}
