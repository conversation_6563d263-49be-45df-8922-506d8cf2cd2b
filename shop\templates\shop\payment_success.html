<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Dry Fish Shop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .success-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        .content {
            padding: 30px;
        }
        .payment-details {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #28a745;
        }
        .transaction-id {
            font-family: monospace;
            background: #e3f2fd;
            padding: 8px 12px;
            border-radius: 6px;
            font-weight: bold;
            color: #1976d2;
        }
        .next-steps {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid #28a745;
        }
        .next-steps h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        .next-steps ul {
            margin-left: 20px;
            color: #155724;
        }
        .next-steps li {
            margin-bottom: 8px;
        }
        .btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .contact-info {
            background: #fff3cd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid #ffc107;
        }
        .whatsapp-btn {
            background: #25d366;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
            transition: background 0.3s;
        }
        .whatsapp-btn:hover {
            background: #128c7e;
        }
        .celebration {
            text-align: center;
            margin: 20px 0;
        }
        .celebration span {
            font-size: 30px;
            animation: float 2s ease-in-out infinite;
            display: inline-block;
            margin: 0 5px;
        }
        .celebration span:nth-child(2) { animation-delay: 0.2s; }
        .celebration span:nth-child(3) { animation-delay: 0.4s; }
        .celebration span:nth-child(4) { animation-delay: 0.6s; }
        .celebration span:nth-child(5) { animation-delay: 0.8s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">🎉</div>
            <h1>Payment Successful!</h1>
            <p>Your order has been confirmed</p>
        </div>
        
        <div class="content">
            <!-- Celebration Animation -->
            <div class="celebration">
                <span>🎊</span>
                <span>✨</span>
                <span>🎉</span>
                <span>✨</span>
                <span>🎊</span>
            </div>

            <!-- Payment Details -->
            <div class="payment-details">
                <h2>Payment Details</h2>
                <div class="detail-row">
                    <span>Order ID:</span>
                    <span>#{{ order.id }}</span>
                </div>
                <div class="detail-row">
                    <span>Product:</span>
                    <span>{{ order.get_product_display }}</span>
                </div>
                <div class="detail-row">
                    <span>Quantity:</span>
                    <span>{{ order.quantity }} kg</span>
                </div>
                <div class="detail-row">
                    <span>Payment Method:</span>
                    <span>{{ order.get_payment_method_display }}</span>
                </div>
                {% if order.transaction_id %}
                <div class="detail-row">
                    <span>Transaction ID:</span>
                    <span class="transaction-id">{{ order.transaction_id }}</span>
                </div>
                {% endif %}
                <div class="detail-row">
                    <span>Amount Paid:</span>
                    <span>₹{{ order.total_amount }}</span>
                </div>
                <div class="detail-row">
                    <span>Payment Status:</span>
                    <span>✅ Completed</span>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🚀 What happens next?</h3>
                <ul>
                    <li>You will receive an order confirmation SMS shortly</li>
                    <li>Our team will contact you within 2 hours to confirm delivery details</li>
                    <li>Your fresh dry fish will be prepared and packed</li>
                    <li>Delivery will be completed within 2-3 business days</li>
                    <li>You can track your order status in your account</li>
                </ul>
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <h3>📞 Need Help?</h3>
                <p>If you have any questions about your order, feel free to contact us:</p>
                <a href="https://wa.me/************?text=Hi, I have a question about my order #{{ order.id }}" class="whatsapp-btn" target="_blank">
                    📱 WhatsApp Support
                </a>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin-top: 30px;">
                <a href="{% url 'home' %}" class="btn">Continue Shopping</a>
                <a href="{% url 'view_orders' %}" class="btn btn-secondary">View My Orders</a>
            </div>

            <!-- Thank You Message -->
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                <h3 style="color: #28a745;">Thank you for choosing Dry Fish Shop! 🐟</h3>
                <p style="color: #666; margin-top: 10px;">We appreciate your business and look forward to serving you again.</p>
            </div>
        </div>
    </div>
</body>
</html>
