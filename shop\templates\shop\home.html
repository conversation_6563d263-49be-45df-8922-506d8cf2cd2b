{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dry Fish Shop - Fresh & Quality Dry Fish</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> Dry Fish Shop
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="#products">Products</a></li>
                <li><a href="/order/">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/export/">Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                    <li><a href="/accounts/signup/">Sign Up</a></li>
                {% endif %}
                <li><a href="#contact">Contact</a></li>
            </ul>
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="fas fa-bars"></i>
            </button>
            <div class="mobile-menu" id="mobileMenu">
                <a href="/">Home</a>
                <a href="#products">Products</a>
                <a href="/order/">Order</a>
                {% if user.is_authenticated %}
                    <a href="/export/">Orders</a>
                    <a href="/accounts/logout/">Logout</a>
                {% else %}
                    <a href="/accounts/login/">Login</a>
                    <a href="/accounts/signup/">Sign Up</a>
                {% endif %}
                <a href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="fade-in-up">Welcome to Our Dry Fish Shop</h1>
            <p class="fade-in-up">Fresh, Quality Dry Fish from the Coast to Your Kitchen</p>
            {% if user.is_authenticated %}
                <p class="fade-in-up">Welcome back, {{ user.first_name|default:user.email|default:user.username }}!</p>
            {% endif %}
            <a href="/order/" class="btn btn-primary fade-in-up">Order Now</a>
        </div>
    </section>

    <!-- Messages -->
    {% if messages %}
        <div class="container">
            <div class="messages">
                {% for message in messages %}
                    <div class="message success">
                        <i class="fas fa-check-circle"></i> {{ message }}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- Products Section -->
    <section id="products" class="container">
        <div class="text-center mb-4">
            <h2 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 1rem;">Our Premium Products</h2>
            <p style="color: #6b7280; font-size: 1.1rem;">Handpicked and carefully dried for the best taste</p>
        </div>

        <div class="product-grid">
            {% for product in products %}
            <div class="product-card fade-in-up">
                <img src="{% static 'shop/images/' %}{{ product|slugify }}.jpg" alt="{{ product }}">
                <div class="product-info">
                    <h2>{{ product|title }}</h2>
                    <div class="product-description">
                        {% if product == "motha kendai" %}
                            <p>Traditional flavor, perfect for spicy South Indian curries and authentic coastal recipes.</p>
                        {% elif product == "netthili" %}
                            <p>Small, crispy dry fish perfect for frying and side dishes, loved across Tamil Nadu.</p>
                        {% elif product == "vaalai" %}
                            <p>Soft texture and rich taste, ideal for gravies and mixed rice preparations.</p>
                        {% elif product == "goa netthili" %}
                            <p>Coastal flavors with a slightly larger size and saltier taste than regular Netthili.</p>
                        {% elif product == "yeera" %}
                            <p>Intense umami flavor perfect for chutneys, stir-fries, and traditional seafood recipes.</p>
                        {% endif %}
                    </div>
                    <a href="/order/" class="btn btn-primary">Order Now</a>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>

    <!-- Call to Action -->
    <section class="container text-center" style="padding: 3rem 0;">
        <h2 style="color: #1f2937; margin-bottom: 1rem;">Ready to Order?</h2>
        <p style="color: #6b7280; margin-bottom: 2rem;">Get fresh, quality dry fish delivered to your doorstep</p>
        <a href="/order/" class="order-btn">
            <i class="fas fa-shopping-cart"></i> Place Your Order
        </a>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><i class="fas fa-fish"></i> Dry Fish Shop</h3>
                    <p>Your trusted source for premium quality dry fish. Fresh from the coast, delivered with care.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p><i class="fas fa-map-marker-alt"></i> Coastal Market, Tamil Nadu</p>
                    <p><i class="fas fa-phone"></i> +91 XXXXXXXXXX</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <p><a href="/">Home</a></p>
                    <p><a href="/order/">Order</a></p>
                    <p><a href="/accounts/login/">Login</a></p>
                    <p><a href="/accounts/signup/">Sign Up</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Dry Fish Shop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/91XXXXXXXXXX" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            mobileMenu.classList.toggle('active');
        }

        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
    </script>
</body>
</html>
