<!DOCTYPE html>
<html>
<head>
    <title>Login - Dry Fish Shop</title>
    <link rel="stylesheet" href="/static/shop/style.css">
    <style>
        .login-container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            margin-bottom: 15px;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #007bff;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Login to Dry Fish Shop</h2>
        
        {% if form.errors %}
            <div class="error">
                <p>Please correct the errors below:</p>
                {{ form.errors }}
            </div>
        {% endif %}
        
        {% if messages %}
            {% for message in messages %}
                <div class="error">{{ message }}</div>
            {% endfor %}
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Username:</label>
                {{ form.username }}
            </div>
            
            <div class="form-group">
                <label for="{{ form.password.id_for_label }}">Password:</label>
                {{ form.password }}
            </div>
            
            <button type="submit" class="btn">Login</button>
            
            <input type="hidden" name="next" value="{{ next }}" />
        </form>
        
        <div class="back-link">
            <a href="/">← Back to Home</a>
        </div>
    </div>
</body>
</html>
