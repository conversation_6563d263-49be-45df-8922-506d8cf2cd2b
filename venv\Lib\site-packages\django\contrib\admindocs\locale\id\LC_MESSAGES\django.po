# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2015-2016
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2011-2012
# <AUTHOR> <EMAIL>, 2013-2014,2016
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-11-18 03:39+0000\n"
"Last-Translator: sage <<EMAIL>>\n"
"Language-Team: Indonesian (http://www.transifex.com/django/django/language/"
"id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Administrative Documentation"
msgstr "Dokumentasi Administrasi"

msgid "Home"
msgstr "Beranda"

msgid "Documentation"
msgstr "Dokumentasi"

msgid "Bookmarklets"
msgstr "Bookmarklet"

msgid "Documentation bookmarklets"
msgstr "Bookmarklet dokumentasi"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Untuk memasang bookmarklet, tarik tautan ke bilah markah, atau klik kanan "
"tautan dan tambahkan ke markah Anda. Sekarang Anda dapat memilih bookmarklet "
"dari semua halaman di situs."

msgid "Documentation for this page"
msgstr "Dokumentasi untuk laman ini"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Buka dari laman apa saja ke laman dokumentasi untuk view yang menghasilkan "
"laman tersebut."

msgid "Tags"
msgstr "Tag"

msgid "List of all the template tags and their functions."
msgstr "Daftar seluruh tag templat dan fungsinya."

msgid "Filters"
msgstr "Filter"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filter merupakan tindakan yang dapat diterapkan pada variabel dalam sebuah "
"templat untuk mengubah keluaran."

msgid "Models"
msgstr "Model"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Model merupakan deskripsi semua objek dalam sistem dan bidang yang terkait. "
"Setiap model memiliki beberapa bidang yang dapat diakses sebagai variabel "
"templat"

msgid "Views"
msgstr "View"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Setiap laman pada situs publik dihasilkan oleh sebuah 'view'. 'View' "
"mendefinisikan templat mana yang digunakan untuk menghasilkan laman dan "
"objek mana yang tersedia untuk templat tersebut."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Alat untuk peramban Anda untuk mempercepat akses fungsionalitas admin. "

msgid "Please install docutils"
msgstr "Instal docutils."

#, python-format
msgid ""
"The admin documentation system requires Python's <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Sistem dokumentasi admin membutuhkan pustaka <a href=\"%(link)s\">docutils</"
"a> dari Python."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Ajukan permintaan kepada administrator Anda untuk menginstal <a href="
"\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Bidang"

msgid "Field"
msgstr "Bidang"

msgid "Type"
msgstr "Tipe"

msgid "Description"
msgstr "Deskripsi"

msgid "Methods with arguments"
msgstr "Metode dengan argumen"

msgid "Method"
msgstr "Metode"

msgid "Arguments"
msgstr "Argumen"

msgid "Back to Model documentation"
msgstr "Kembali ke Dokumentasi Model"

msgid "Model documentation"
msgstr "Dokumentasi model"

msgid "Model groups"
msgstr "Grup model"

msgid "Templates"
msgstr "Templat"

#, python-format
msgid "Template: %(name)s"
msgstr "Templat: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Templat: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Lokasi pencarian templat <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(tidak ada)"

msgid "Back to Documentation"
msgstr "Kembali ke Dokumentasi"

msgid "Template filters"
msgstr "Filter templat"

msgid "Template filter documentation"
msgstr "Dokumentasi filter templat"

msgid "Built-in filters"
msgstr "Filter bawaan"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Untuk menggunakan filter ini, sertakan <code>%(code)s</code> pada templat "
"Anda sebelum menggunakan filternya."

msgid "Template tags"
msgstr "Tag templat"

msgid "Template tag documentation"
msgstr "Dokumentasi tag templat"

msgid "Built-in tags"
msgstr "Tag bawaan"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Untuk menggunakan tag ini, sertakan <code>%(code)s</code> pada templat Anda "
"sebelum menggunakan tagnya."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Konteks:"

msgid "Templates:"
msgstr "Templat:"

msgid "Back to View documentation"
msgstr "Kembali ke dokumentasi Tampilan"

msgid "View documentation"
msgstr "Dokumentasi view"

msgid "Jump to namespace"
msgstr "Loncat ke namespace"

msgid "Empty namespace"
msgstr "Namespace kosong"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "View berdasarkan namespace %(name)s"

msgid "Views by empty namespace"
msgstr "View berdasarkan namespace kosong"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Fungsi view: <code>%(full_name)s</code>. Nama: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Aplikasi %(app_label)r tidak ditemukan"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r tidak ditemukan di aplikasi %(app_label)r"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "objek `%(app_label)s.%(data_type)s` yang terkait"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "obyek `%(app_label)s.%(object_name)s` yang terkait"

#, python-format
msgid "all %s"
msgstr "semua %s"

#, python-format
msgid "number of %s"
msgstr "jumlah %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s bukan berupa objek urlpattern"
