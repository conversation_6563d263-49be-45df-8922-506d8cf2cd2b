{% extends "admin/base_site.html" %}
{% load i18n static %}
{% block extrastyle %}{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">{% endblock %}
{% block userlinks %}{% url 'django-admindocs-docroot' as docsroot %}{% if docsroot %}<a href="{{ docsroot }}">{% translate 'Documentation' %}</a> / {% endif %} {% translate 'Change password' %} / <a href="{% url 'admin:logout' %}">{% translate 'Log out' %}</a>{% endblock %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; {% translate 'Password change' %}
</div>
{% endblock %}

{% block content %}<div id="content-main">

<form method="post">{% csrf_token %}
<div>
{% if form.errors %}
    <p class="errornote">
    {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
    </p>
{% endif %}


<p>{% translate 'Please enter your old password, for security’s sake, and then enter your new password twice so we can verify you typed it in correctly.' %}</p>

<fieldset class="module aligned wide">

<div class="form-row">
    {{ form.old_password.errors }}
    {{ form.old_password.label_tag }} {{ form.old_password }}
</div>

<div class="form-row">
    {{ form.new_password1.errors }}
    {{ form.new_password1.label_tag }} {{ form.new_password1 }}
    {% if form.new_password1.help_text %}
    <div class="help">{{ form.new_password1.help_text|safe }}</div>
    {% endif %}
</div>

<div class="form-row">
{{ form.new_password2.errors }}
    {{ form.new_password2.label_tag }} {{ form.new_password2 }}
    {% if form.new_password2.help_text %}
    <div class="help">{{ form.new_password2.help_text|safe }}</div>
    {% endif %}
</div>

</fieldset>

<div class="submit-row">
    <input type="submit" value="{% translate 'Change my password' %}" class="default">
</div>

</div>
</form></div>

{% endblock %}
