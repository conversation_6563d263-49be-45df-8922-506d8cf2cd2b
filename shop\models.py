from django.db import models

class Order(models.Model):
    PRODUCT_CHOICES = [
        ('motha kendai', '<PERSON><PERSON>'),
        ('netthili', 'Netthi<PERSON>'),
        ('vaalai', 'Vaal<PERSON>'),
        ('goa netthili', 'Goa Netthili'),
        ('<PERSON><PERSON>', '<PERSON><PERSON>'),
    ]
    name = models.CharField(max_length=100)
    mobile = models.CharField(max_length=15)
    address = models.TextField()
    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    quantity = models.PositiveIntegerField()
    paid = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.name} - {self.product}"
