from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

class Order(models.Model):
    PRODUCT_CHOICES = [
        ('motha kendai', '<PERSON><PERSON>'),
        ('netthili', '<PERSON>thi<PERSON>'),
        ('vaalai', '<PERSON>aal<PERSON>'),
        ('goa netthili', '<PERSON> Netthili'),
        ('yeera', '<PERSON>era'),
    ]
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=100)
    mobile = models.CharField(max_length=15)
    address = models.TextField()
    product = models.CharField(max_length=50, choices=PRODUCT_CHOICES)
    quantity = models.PositiveIntegerField()
    paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.name} - {self.product}"
