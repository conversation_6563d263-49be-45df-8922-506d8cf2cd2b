<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Dry Fish Shop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .payment-methods {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        .payment-option {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .payment-option:hover {
            border-color: #4285f4;
            background: #f0f7ff;
        }
        .payment-option.selected {
            border-color: #4285f4;
            background: #e3f2fd;
        }
        .payment-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        .google-pay { background: #4285f4; }
        .phonepe { background: #5f259f; }
        .paytm { background: #00baf2; }
        .upi { background: #ff6b35; }
        .cod { background: #28a745; }
        .bank { background: #6c757d; }
        
        .payment-details h3 {
            margin-bottom: 5px;
            color: #333;
        }
        .payment-details p {
            color: #666;
            font-size: 14px;
        }
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #3367d6;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #4285f4;
            text-decoration: none;
        }
        .price-breakdown {
            border-top: 1px solid #eee;
            padding-top: 15px;
            margin-top: 15px;
        }
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .total-row {
            font-weight: bold;
            font-size: 18px;
            color: #333;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Complete Your Payment</h1>
            <p>Secure payment for your dry fish order</p>
        </div>
        
        <div class="content">
            <!-- Order Summary -->
            <div class="order-summary">
                <h2>Order Summary</h2>
                <div style="margin: 15px 0;">
                    <strong>Product:</strong> {{ order.get_product_display }}<br>
                    <strong>Quantity:</strong> {{ order.quantity }} kg<br>
                    <strong>Customer:</strong> {{ order.name }}<br>
                    <strong>Mobile:</strong> {{ order.mobile }}
                </div>
                
                <div class="price-breakdown">
                    <div class="price-row">
                        <span>{{ order.get_product_display }} ({{ order.quantity }} kg × ₹{{ order.get_product_price }})</span>
                        <span>₹{{ order.total_amount }}</span>
                    </div>
                    {% if order.payment_method == 'cash_on_delivery' %}
                    <div class="price-row">
                        <span>COD Charges</span>
                        <span>₹50</span>
                    </div>
                    {% endif %}
                    <div class="price-row total-row">
                        <span>Total Amount</span>
                        <span>₹{% if order.payment_method == 'cash_on_delivery' %}{{ order.total_amount|add:50 }}{% else %}{{ order.total_amount }}{% endif %}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <form method="post" id="paymentForm">
                {% csrf_token %}
                <h2>Choose Payment Method</h2>
                
                <div class="payment-methods">
                    <!-- Online Payment Options -->
                    <div class="payment-option" data-method="google_pay">
                        <div class="payment-icon google-pay">💳</div>
                        <div class="payment-details">
                            <h3>Google Pay</h3>
                            <p>Pay securely with Google Pay</p>
                        </div>
                        <input type="radio" name="payment_method" value="google_pay" style="margin-left: auto;">
                    </div>
                    
                    <div class="payment-option" data-method="phonepe">
                        <div class="payment-icon phonepe">📱</div>
                        <div class="payment-details">
                            <h3>PhonePe</h3>
                            <p>Quick payment via PhonePe</p>
                        </div>
                        <input type="radio" name="payment_method" value="phonepe" style="margin-left: auto;">
                    </div>
                    
                    <div class="payment-option" data-method="paytm">
                        <div class="payment-icon paytm">💰</div>
                        <div class="payment-details">
                            <h3>Paytm</h3>
                            <p>Pay using Paytm wallet or UPI</p>
                        </div>
                        <input type="radio" name="payment_method" value="paytm" style="margin-left: auto;">
                    </div>
                    
                    <div class="payment-option" data-method="upi">
                        <div class="payment-icon upi">🏦</div>
                        <div class="payment-details">
                            <h3>UPI</h3>
                            <p>Pay via any UPI app</p>
                        </div>
                        <input type="radio" name="payment_method" value="upi" style="margin-left: auto;">
                    </div>
                    
                    <!-- Offline Payment Options -->
                    <div class="payment-option" data-method="cash_on_delivery">
                        <div class="payment-icon cod">💵</div>
                        <div class="payment-details">
                            <h3>Cash on Delivery</h3>
                            <p>Pay when your order is delivered (+₹50 COD charges)</p>
                        </div>
                        <input type="radio" name="payment_method" value="cash_on_delivery" style="margin-left: auto;">
                    </div>
                    
                    <div class="payment-option" data-method="bank_transfer">
                        <div class="payment-icon bank">🏛️</div>
                        <div class="payment-details">
                            <h3>Bank Transfer</h3>
                            <p>Transfer to our bank account</p>
                        </div>
                        <input type="radio" name="payment_method" value="bank_transfer" style="margin-left: auto;">
                    </div>
                </div>
                
                <button type="submit" class="btn" id="proceedBtn" disabled>
                    Proceed to Payment
                </button>
            </form>
            
            <div class="back-link">
                <a href="{% url 'order' %}">← Back to Order</a>
            </div>
        </div>
    </div>

    <script>
        // Payment method selection
        const paymentOptions = document.querySelectorAll('.payment-option');
        const proceedBtn = document.getElementById('proceedBtn');
        
        paymentOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                paymentOptions.forEach(opt => opt.classList.remove('selected'));
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // Enable proceed button
                proceedBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
