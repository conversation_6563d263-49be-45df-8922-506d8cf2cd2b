{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Dry Fish - Dry Fish Shop</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> Dry Fish Shop
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/#products">Products</a></li>
                <li><a href="/order/" class="active">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/export/">Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="container" style="padding-top: 2rem;">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 style="font-size: 2.5rem; color: #1f2937; margin-bottom: 0.5rem;">
                <i class="fas fa-shopping-cart"></i> Place Your Order
            </h1>
            <p style="color: #6b7280; font-size: 1.1rem;">Fill in your details to get fresh dry fish delivered</p>
        </div>

        <!-- User Welcome Message -->
        {% if user.is_authenticated %}
            <div style="background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 2rem; border-left: 4px solid #3b82f6;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-user-check" style="color: #3b82f6;"></i>
                    <strong style="color: #1e40af;">Welcome, {{ user.first_name|default:user.email }}!</strong>
                </div>
                <p style="margin: 0.5rem 0 0 0; color: #1e40af;">You're logged in with your Google account. Your order will be linked to your account for easy tracking.</p>
            </div>
        {% endif %}

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Order Form -->
        <div class="form-container">
            <form method="post" class="fade-in-up">
                {% csrf_token %}

                <div class="form-group">
                    <label for="name">
                        <i class="fas fa-user"></i> Full Name
                    </label>
                    <input type="text" id="name" name="name" class="form-control"
                           placeholder="Enter your full name"
                           value="{{ user_data.name|default:'' }}" required>
                </div>

                <div class="form-group">
                    <label for="mobile">
                        <i class="fas fa-phone"></i> Mobile Number
                    </label>
                    <input type="tel" id="mobile" name="mobile" class="form-control"
                           placeholder="Enter your mobile number" required>
                </div>

                <div class="form-group">
                    <label for="address">
                        <i class="fas fa-map-marker-alt"></i> Delivery Address
                    </label>
                    <textarea id="address" name="address" class="form-control"
                              placeholder="Enter your complete delivery address" required></textarea>
                </div>

                <div class="form-group">
                    <label for="product">
                        <i class="fas fa-fish"></i> Select Product
                    </label>
                    <select id="product" name="product" class="form-control" required>
                        <option value="">Choose a product...</option>
                        <option value="motha kendai">Motha Kendai - Traditional favorite for curries</option>
                        <option value="netthili">Netthili - Perfect for frying and side dishes</option>
                        <option value="vaalai">Vaalai - Soft texture, ideal for gravies</option>
                        <option value="goa netthili">Goa Netthili - Coastal flavors, larger size</option>
                        <option value="yeera">Yeera - Intense umami for chutneys</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="quantity">
                        <i class="fas fa-weight-hanging"></i> Quantity (kg)
                    </label>
                    <input type="number" id="quantity" name="quantity" class="form-control"
                           placeholder="Enter quantity in kg" min="0.5" step="0.5" required>
                </div>

                <div class="form-group">
                    <label for="paid">
                        <i class="fas fa-credit-card"></i> Payment Status
                    </label>
                    <select id="paid" name="paid" class="form-control">
                        <option value="no">Payment Pending</option>
                        <option value="yes">Payment Completed</option>
                    </select>
                </div>

                <div class="text-center">
                    <button type="submit" class="btn btn-success" style="padding: 1rem 2rem; font-size: 1.1rem;">
                        <i class="fas fa-paper-plane"></i> Submit Order
                    </button>
                </div>
            </form>
        </div>

        <!-- Back to Home -->
        <div class="text-center" style="margin: 2rem 0;">
            <a href="/" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/91XXXXXXXXXX" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const mobile = document.getElementById('mobile').value;
            const mobilePattern = /^[6-9]\d{9}$/;

            if (!mobilePattern.test(mobile)) {
                e.preventDefault();
                alert('Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9');
                return false;
            }
        });
    </script>
</body>
</html>
