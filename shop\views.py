from django.shortcuts import render, redirect
from django.contrib import messages
from .models import Order
from django.http import HttpResponse
import csv

def home(request):
    return render(request, 'shop/home.html')

def order(request):
    if request.method == 'POST':
        order = Order.objects.create(
            name=request.POST['name'],
            mobile=request.POST['mobile'],
            address=request.POST['address'],
            product=request.POST['product'],
            quantity=request.POST['quantity'],
            paid=True if request.POST.get('paid') == 'yes' else False
        )
        messages.success(request, 'Your order was placed successfully!')
        return redirect('home')
    return render(request, 'shop/order.html')

def export_orders(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="orders.csv"'
    writer = csv.writer(response)
    writer.writerow(['Name', 'Mobile', 'Address', 'Product', 'Quantity', 'Paid'])
    for order in Order.objects.all():
        writer.writerow([order.name, order.mobile, order.address, order.product, order.quantity, order.paid])
    return response
