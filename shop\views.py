from django.shortcuts import render, redirect
from django.contrib import messages
from .models import Order
from django.http import HttpResponse
import csv

def home(request):
    # Get product choices from the model to ensure consistency
    from .models import Order
    products = [choice[0] for choice in Order.PRODUCT_CHOICES]
    return render(request, 'shop/home.html', {'products': products})

def order(request):
    if request.method == 'POST':
        try:
            Order.objects.create(
                name=request.POST['name'],
                mobile=request.POST['mobile'],
                address=request.POST['address'],
                product=request.POST['product'],
                quantity=int(request.POST['quantity']),
                paid=True if request.POST.get('paid') == 'yes' else False
            )
            messages.success(request, 'Your order was placed successfully!')
            return redirect('home')
        except (ValueError, KeyError) as e:
            messages.error(request, 'Please fill all fields correctly.')
    return render(request, 'shop/order.html')

def export_orders(request):
    if not request.user.is_authenticated:
        return redirect('home')

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="orders.csv"'
    writer = csv.writer(response)
    writer.writerow(['Name', 'Mobile', 'Address', 'Product', 'Quantity', 'Paid'])
    for order in Order.objects.all():
        writer.writerow([order.name, order.mobile, order.address, order.product, order.quantity, order.paid])
    return response
