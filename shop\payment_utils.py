"""
Payment utility functions for the dry fish shop
"""
import uuid
import json
from django.conf import settings
from django.urls import reverse


class PaymentProcessor:
    """Handle different payment methods"""
    
    @staticmethod
    def generate_transaction_id():
        """Generate a unique transaction ID"""
        return f"DFS_{uuid.uuid4().hex[:12].upper()}"
    
    @staticmethod
    def get_google_pay_config():
        """Get Google Pay configuration"""
        return {
            'environment': 'TEST',  # Change to 'PRODUCTION' for live
            'apiVersion': 2,
            'apiVersionMinor': 0,
            'allowedPaymentMethods': [{
                'type': 'CARD',
                'parameters': {
                    'allowedAuthMethods': ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
                    'allowedCardNetworks': ['MASTERCARD', 'VISA', 'RUPAY']
                },
                'tokenizationSpecification': {
                    'type': 'PAYMENT_GATEWAY',
                    'parameters': {
                        'gateway': 'example',  # Replace with your payment gateway
                        'gatewayMerchantId': 'exampleGatewayMerchantId'  # Replace with your merchant ID
                    }
                }
            }],
            'merchantInfo': {
                'merchantId': '********************',  # Replace with your Google Pay merchant ID
                'merchantName': 'Dry Fish Shop'
            }
        }
    
    @staticmethod
    def create_payment_request(order):
        """Create payment request data"""
        return {
            'order_id': order.id,
            'amount': float(order.total_amount),
            'currency': 'INR',
            'description': f'{order.product} - {order.quantity}kg',
            'customer': {
                'name': order.name,
                'mobile': order.mobile,
                'email': order.user.email if order.user else ''
            }
        }
    
    @staticmethod
    def get_upi_payment_url(order):
        """Generate UPI payment URL"""
        upi_id = "dryfishshop@paytm"  # Replace with your UPI ID
        amount = order.total_amount
        transaction_id = PaymentProcessor.generate_transaction_id()
        
        # Update order with transaction ID
        order.transaction_id = transaction_id
        order.save()
        
        upi_url = f"upi://pay?pa={upi_id}&pn=Dry Fish Shop&am={amount}&cu=INR&tn=Order {order.id}&tr={transaction_id}"
        return upi_url
    
    @staticmethod
    def verify_payment(transaction_id, payment_method):
        """Verify payment status (mock implementation)"""
        # In a real implementation, you would call the payment gateway API
        # For now, we'll return a mock response
        return {
            'status': 'success',
            'transaction_id': transaction_id,
            'payment_method': payment_method,
            'verified': True
        }


class OfflinePaymentHandler:
    """Handle offline payment methods"""
    
    @staticmethod
    def get_bank_details():
        """Get bank account details for bank transfer"""
        return {
            'bank_name': 'State Bank of India',
            'account_holder': 'Dry Fish Shop',
            'account_number': '**********',  # Replace with actual account
            'ifsc_code': 'SBIN0001234',     # Replace with actual IFSC
            'branch': 'Main Branch'
        }
    
    @staticmethod
    def get_cod_instructions():
        """Get Cash on Delivery instructions"""
        return {
            'title': 'Cash on Delivery',
            'instructions': [
                'Pay cash when your order is delivered',
                'Please keep exact change ready',
                'Our delivery person will provide a receipt',
                'COD available within city limits only'
            ],
            'cod_charges': 50  # COD handling charges
        }
    
    @staticmethod
    def process_offline_payment(order, payment_method):
        """Process offline payment methods"""
        if payment_method == 'cash_on_delivery':
            order.payment_status = 'pending'
            order.transaction_id = PaymentProcessor.generate_transaction_id()
        elif payment_method == 'bank_transfer':
            order.payment_status = 'pending'
            order.transaction_id = PaymentProcessor.generate_transaction_id()
        
        order.save()
        return True
