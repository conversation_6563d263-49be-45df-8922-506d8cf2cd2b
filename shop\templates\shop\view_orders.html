{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Orders - Dry Fish Shop</title>
    <link rel="stylesheet" href="{% static 'shop/style.css' %}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .orders-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }
        .orders-header {
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .orders-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }
        .orders-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 3rem;
        }
        .order-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #4285f4;
        }
        .order-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        .order-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        .order-id {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .order-body {
            padding: 1.5rem;
        }
        .order-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        .detail-label {
            font-size: 0.85rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }
        .detail-value {
            font-weight: 600;
            color: #495057;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .order-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #dee2e6;
        }
        .btn-action {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }
        .btn-primary {
            background: #4285f4;
            color: white;
        }
        .btn-primary:hover {
            background: #3367d6;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-outline {
            background: transparent;
            color: #6c757d;
            border: 2px solid #dee2e6;
        }
        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .empty-icon {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 1rem;
        }
        .offline-payment-info {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            border-left: 5px solid #ffc107;
        }
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        .payment-method {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .method-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .method-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .bank-icon { background: #6c757d; }
        .cod-icon { background: #28a745; }
        .upi-icon { background: #ff6b35; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="/" class="logo">
                <i class="fas fa-fish"></i> Dry Fish Shop
            </a>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/products/">Products</a></li>
                <li><a href="/order/">Order</a></li>
                {% if user.is_authenticated %}
                    <li><a href="/orders/" class="active">View Orders</a></li>
                    <li><a href="/accounts/logout/">Logout</a></li>
                {% else %}
                    <li><a href="/accounts/login/">Login</a></li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <div class="orders-container">
        <!-- Header -->
        <div class="orders-header">
            <h1><i class="fas fa-clipboard-list"></i> My Orders</h1>
            <p>Track your dry fish orders and payment status</p>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {% if message.tags == 'error' %}error{% else %}success{% endif %}">
                        <i class="fas {% if message.tags == 'error' %}fa-exclamation-circle{% else %}fa-check-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Orders Grid -->
        {% if orders %}
            <div class="orders-grid">
                {% for order in orders %}
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-id">
                                <i class="fas fa-receipt"></i> Order #{{ order.id }}
                            </div>
                            <div class="order-date">
                                <i class="fas fa-calendar"></i> {{ order.created_at|date:"F d, Y - g:i A" }}
                            </div>
                        </div>
                        
                        <div class="order-body">
                            <div class="order-details">
                                <div class="detail-item">
                                    <span class="detail-label">Product</span>
                                    <span class="detail-value">{{ order.get_product_display }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Quantity</span>
                                    <span class="detail-value">{{ order.quantity }} kg</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Total Amount</span>
                                    <span class="detail-value">₹{{ order.total_amount }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Payment Method</span>
                                    <span class="detail-value">{{ order.get_payment_method_display }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Payment Status</span>
                                    <span class="status-badge status-{{ order.payment_status }}">
                                        {% if order.payment_status == 'completed' %}
                                            <i class="fas fa-check-circle"></i> Completed
                                        {% elif order.payment_status == 'pending' %}
                                            <i class="fas fa-clock"></i> Pending
                                        {% elif order.payment_status == 'failed' %}
                                            <i class="fas fa-times-circle"></i> Failed
                                        {% else %}
                                            <i class="fas fa-question-circle"></i> {{ order.get_payment_status_display }}
                                        {% endif %}
                                    </span>
                                </div>
                                {% if order.transaction_id %}
                                <div class="detail-item">
                                    <span class="detail-label">Transaction ID</span>
                                    <span class="detail-value" style="font-family: monospace;">{{ order.transaction_id }}</span>
                                </div>
                                {% endif %}
                            </div>

                            <div class="order-actions">
                                {% if order.payment_status == 'pending' and order.payment_method != 'cash_on_delivery' %}
                                    <a href="{% url 'payment_process' order.id %}" class="btn-action btn-primary">
                                        <i class="fas fa-credit-card"></i> Complete Payment
                                    </a>
                                {% endif %}
                                
                                {% if order.payment_status == 'completed' %}
                                    <button class="btn-action btn-success" disabled>
                                        <i class="fas fa-check"></i> Payment Completed
                                    </button>
                                {% endif %}
                                
                                <a href="{% url 'payment_confirmation' order.id %}" class="btn-action btn-outline">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3>No Orders Yet</h3>
                <p>You haven't placed any orders yet. Start shopping for fresh dry fish!</p>
                <a href="/order/" class="btn-action btn-primary" style="margin-top: 1rem;">
                    <i class="fas fa-plus"></i> Place Your First Order
                </a>
            </div>
        {% endif %}

        <!-- Offline Payment Information -->
        <div class="offline-payment-info">
            <h2><i class="fas fa-info-circle"></i> Offline Payment Methods</h2>
            <p>For orders with pending payments, you can use these offline payment methods:</p>
            
            <div class="payment-methods">
                <!-- Bank Transfer -->
                <div class="payment-method">
                    <div class="method-header">
                        <div class="method-icon bank-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div>
                            <h4>Bank Transfer</h4>
                            <p>Direct bank account transfer</p>
                        </div>
                    </div>
                    <div>
                        <p><strong>Bank:</strong> State Bank of India</p>
                        <p><strong>Account Holder:</strong> Dry Fish Shop</p>
                        <p><strong>Account Number:</strong> **********</p>
                        <p><strong>IFSC Code:</strong> SBIN0001234</p>
                        <p><strong>Branch:</strong> Main Branch</p>
                        <small style="color: #6c757d;">Send payment screenshot to WhatsApp after transfer</small>
                    </div>
                </div>

                <!-- Cash on Delivery -->
                <div class="payment-method">
                    <div class="method-header">
                        <div class="method-icon cod-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <h4>Cash on Delivery</h4>
                            <p>Pay when order is delivered</p>
                        </div>
                    </div>
                    <div>
                        <p><strong>COD Charges:</strong> ₹50</p>
                        <p><strong>Delivery Time:</strong> 2-3 business days</p>
                        <p><strong>Coverage:</strong> City limits only</p>
                        <p><strong>Payment:</strong> Cash to delivery person</p>
                        <small style="color: #6c757d;">Please keep exact change ready</small>
                    </div>
                </div>

                <!-- UPI Payment -->
                <div class="payment-method">
                    <div class="method-header">
                        <div class="method-icon upi-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div>
                            <h4>UPI Payment</h4>
                            <p>Pay via any UPI app</p>
                        </div>
                    </div>
                    <div>
                        <p><strong>UPI ID:</strong> dryfishshop@paytm</p>
                        <p><strong>Supported Apps:</strong> Google Pay, PhonePe, Paytm, BHIM</p>
                        <p><strong>Reference:</strong> Use your Order ID</p>
                        <small style="color: #6c757d;">Send payment screenshot for confirmation</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 3rem 0;">
            <a href="/order/" class="btn-action btn-primary">
                <i class="fas fa-plus"></i> Place New Order
            </a>
            <a href="/" class="btn-action btn-outline">
                <i class="fas fa-home"></i> Back to Home
            </a>
        </div>
    </div>

    <!-- WhatsApp Float -->
    <a href="https://wa.me/919876543210" class="whatsapp-float" target="_blank" title="Chat on WhatsApp">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Back to Top -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // Back to top functionality
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>
