from django.contrib import admin
from django.urls import path, include
from shop import views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', views.home, name='home'),
    path('order/', views.order, name='order'),
    path('orders/', views.view_orders, name='view_orders'),
    path('payment/<int:order_id>/', views.payment, name='payment'),
    path('payment/process/<int:order_id>/', views.payment_process, name='payment_process'),
    path('payment/confirmation/<int:order_id>/', views.payment_confirmation, name='payment_confirmation'),
    path('payment/success/<int:order_id>/', views.payment_success, name='payment_success'),
    path('export/', views.export_orders, name='export_orders'),
    path('accounts/', include('allauth.urls')),
]
