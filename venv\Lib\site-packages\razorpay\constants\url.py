class URL(object):
    BASE_URL = 'https://api.razorpay.com'
    V1 = '/v1'
    V2 = '/v2'
    ORDER_URL = "/orders"
    INVOICE_URL = "/invoices"
    PAYMENT_LINK_URL = "/payment_links"
    PAYMENTS_URL = "/payments"
    REFUNDS_URL = "/refunds"
    CARD_URL = "/cards"
    CUSTOMER_URL = "/customers"
    TRANSFER_URL = "/transfers"
    VIRTUAL_ACCOUNT_URL = "/virtual_accounts"
    SUBSCRIPTION_URL = "/subscriptions"
    ADDON_URL = "/addons"
    PLAN_URL = "/plans"
    SETTLEMENT_URL = "/settlements"
    ITEM_URL = "/items"
    QRCODE_URL = "/payments/qr_codes"
    REGISTRATION_LINK_URL = "/subscription_registration"
    FUND_ACCOUNT_URL = "/fund_accounts"
    ACCOUNT = "/accounts"
    STAKEHOLDER = "/stakeholders"
    PRODUCT = "/products"
    TNC = "/tnc"
    TOKEN = "/tokens"
    IIN = "/iins"
    WEBHOOK = "/webhooks"
    DOCUMENT= "/documents"
    DISPUTE= "/disputes"

